from django.urls import path, include
from rest_framework.routers import DefaultRouter
from apps.cs_manage.views import CsQualityCheckViewSet, CsManageDashboardViewSet, CsEmotionAnalysisViewSet, cs_emotion_analysis_dashboard, cs_emotion_analysis_dashboard_staff_detail

# 创建路由器
router = DefaultRouter()
router.register(r'cs_quality_check', CsQualityCheckViewSet, basename='cs_quality_check')
router.register(r'dashboard', CsManageDashboardViewSet, basename='dashboard')
router.register(r'cs_emotion_analysis', CsEmotionAnalysisViewSet, basename='cs_emotion_analysis')
# URL配置
urlpatterns = [
    # 包含路由器生成的URL
    path('', include(router.urls)),
    path('cs_emotion_analysis_dashboard/', cs_emotion_analysis_dashboard.as_view(), name='cs_emotion_analysis_dashboard'),
    path('cs_emotion_analysis_dashboard/detail/', cs_emotion_analysis_dashboard_staff_detail.as_view(), name='cs_emotion_analysis_dashboard_staff_detail')
]