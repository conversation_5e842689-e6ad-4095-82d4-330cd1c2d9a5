import * as api from './api';
import { dict, UserPageQuery, AddReq, DelReq, EditReq, compute, CreateCrudOptionsProps, CreateCrudOptionsRet } from '@fast-crud/fast-crud';
import { dictionary } from '/@/utils/dictionary';
import { inject, nextTick, ref } from 'vue';
import { successMessage } from '/@/utils/message';
import {auth} from '/@/utils/authFunction';
export const createCrudOptions = function ({ crudExpose, context }: CreateCrudOptionsProps): CreateCrudOptionsRet {
	const pageRequest = async (query: UserPageQuery) => {
		return await api.GetList(query);
	};
	const editRequest = async ({ form, row }: EditReq) => {
		form.id = row.id;
		return await api.UpdateObj(form);
	};
	const delRequest = async ({ row }: DelReq) => {
		return await api.DelObj(row.id);
	};
	const addRequest = async ({ form }: AddReq) => {
		return await api.AddObj(form);
	};


	return {
		crudOptions: {
			request: {
				pageRequest,
				addRequest,
				editRequest,
				delRequest,
			},
			rowHandle: {
				fixed: 'right',
				width: 200,
				buttons: {
					view: {
						show: false,
					},
					edit: {
						iconRight: 'Edit',
						type: 'text',
						// show: auth('game:Update'),
					},
					remove: {
						iconRight: 'Delete',
						type: 'text',
						// show: auth('game:Delete'),
					},
				},
			},
			columns: {
				id: {
					title: 'id',
					readonly: true,
					addForm: { show: false },
					editForm: { show: false },
					search: {
						show: true,
						component: {
							props: {
								clearable: true,
							},
							placeholder: '请输入id',
						},
					},
				},
				name: {
					title: '游戏名称',
					search: {
						show: true,
						component: {
							props: {
								clearable: true,
							},
							placeholder: '请输入游戏名称',
						},
					},
				},
				game_id: {
					title: '游戏ID',
					search: {
						show: true,
						component: {
							props: {
								clearable: true,
							},
							placeholder: '请输入游戏id',
						},
					},
				},
				qiyu_group_name: {
					title: '七鱼组别名称',
					search: {
						show: true,
						component: {
							props: {
								clearable: true,
							},
							placeholder: '请输入七鱼组别名称',
						},
					},
				},
				creator: {
					title: '创建人',
					search: {
						show: true,
						component: {
							props: {
								clearable: true,
							},
						},
					},
					addForm: { show: false },
					editForm: { show: false },
					type: 'dict-select',
					dict: dict({
						url: '/api/system/user/?show_all=all',
						value: 'id',
						label: 'name',
					}),
				},
				create_datetime: {
					title: '创建时间',
					addForm: { show: false },
					editForm: { show: false },
				},

			}
		},
	};
};
