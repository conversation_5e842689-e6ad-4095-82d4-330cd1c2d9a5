<template>
  <!-- 分组数据卡片 -->
  <div v-if="isGroupCard" class="group-data-card bg-white rounded-lg shadow-sm border transition-all duration-200 hover:shadow-md">
    <div class="p-4 border-b border-gray-100">
      <div class="flex items-center justify-between">
        <h3 class="text-base font-semibold text-gray-800 flex items-center">
          <el-icon class="mr-2" :class="groupIconColor">
            <component :is="groupIcon" />
          </el-icon>
          {{ groupTitle }}
        </h3>
        <div v-if="groupTrend" class="flex items-center text-sm">
          <el-icon :class="groupTrend > 0 ? 'text-green-500' : 'text-red-500'" class="mr-1">
            <component :is="groupTrend > 0 ? 'TrendCharts' : 'ArrowDown'" />
          </el-icon>
          <span :class="groupTrend > 0 ? 'text-green-600' : 'text-red-600'">
            {{ Math.abs(groupTrend) }}%
          </span>
        </div>
      </div>
    </div>
    
    <div class="p-4">
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div v-for="item in groupItems" :key="item.key" class="text-center">
          <div class="flex flex-col items-center">
            <div class="w-8 h-8 rounded-full flex items-center justify-center mb-2" :class="getItemBgColor(item.color)">
              <el-icon :size="14" color="#ffffff">
                <component :is="item.icon" />
              </el-icon>
            </div>
            <p class="text-xs text-gray-600 mb-1">{{ item.title }}</p>
            <div v-if="!loading" class="flex items-baseline justify-center">
              <span class="text-lg font-bold" :class="getItemTextColor(item.color)">
                {{ formatValue(item.value) }}
              </span>
              <span v-if="item.suffix" class="text-xs text-gray-500 ml-1">{{ item.suffix }}</span>
            </div>
            <div v-else class="animate-pulse">
              <div class="h-5 bg-gray-200 rounded w-12"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 单个数据卡片 -->
  <div v-else class="data-card bg-white rounded-lg shadow-sm p-4 border-l-4 transition-all duration-200 hover:shadow-md" :class="borderColorClass">
    <div class="flex items-center justify-between">
      <div class="flex-1">
        <p class="text-sm font-medium text-gray-600 mb-1">{{ title }}</p>
        <div class="flex items-baseline">
          <p v-if="!loading" class="text-xl font-bold" :class="textColorClass">
            {{ formattedValue }}
            <span v-if="suffix" class="text-sm font-normal text-gray-500 ml-1">{{ suffix }}</span>
          </p>
          <div v-else class="animate-pulse">
            <div class="h-6 bg-gray-200 rounded w-16"></div>
          </div>
        </div>

        <!-- 趋势指示器 -->
        <div v-if="trend && !loading" class="flex items-center mt-2">
          <el-icon
            :class="[
              'text-xs mr-1',
              trend > 0 ? 'text-green-500' :
              trend < 0 ? 'text-red-500' :
              'text-gray-400'
            ]"
          >
            <component :is="trend > 0 ? 'ArrowUp' : trend < 0 ? 'ArrowDown' : 'Remove'" />
          </el-icon>
          <span
            :class="[
              'text-xs',
              trend > 0 ? 'text-green-600' :
              trend < 0 ? 'text-red-600' :
              'text-gray-500'
            ]"
          >
            {{ Math.abs(trend) }}%
          </span>
          <span class="text-xs text-gray-500 ml-1">vs 昨日</span>
        </div>
      </div>

      <div class="flex-shrink-0 ml-4">
        <div
          class="w-10 h-10 rounded-full flex items-center justify-center"
          :class="backgroundColorClass"
        >
          <el-icon :size="18" color="#ffffff">
            <component :is="icon" />
          </el-icon>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ArrowUp, ArrowDown, Remove, TrendCharts } from '@element-plus/icons-vue'

interface GroupItem {
  key: string
  title: string
  value: number | string
  suffix?: string
  icon: string
  color: string
}

interface Props {
  // 单个卡片属性
  title?: string
  value?: number | string
  suffix?: string
  icon?: string
  color?: 'blue' | 'green' | 'purple' | 'orange' | 'red' | 'yellow' | 'teal' | 'indigo' | 'pink' | 'cyan' | 'lime' | 'rose' | 'emerald'
  loading?: boolean
  trend?: number // 趋势百分比，正数表示上升，负数表示下降
  
  // 分组卡片属性
  isGroupCard?: boolean
  groupTitle?: string
  groupIcon?: string
  groupIconColor?: string
  groupTrend?: number
  groupItems?: GroupItem[]
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  suffix: '',
  trend: undefined,
  isGroupCard: false,
  groupItems: () => []
})

// 格式化数值显示 - 直接显示完整数值，不使用k,w缩写
const formattedValue = computed(() => {
  if (typeof props.value === 'string') {
    return props.value
  }

  // 数字格式化 - 添加千位分隔符
  if (typeof props.value === 'number') {
    return props.value.toLocaleString()
  } else {
    return props.value.toString()
  }
})

// 格式化分组项的数值
const formatValue = (value: number | string) => {
  if (typeof value === 'string') {
    return value
  }
  if (typeof value === 'number') {
    return value.toLocaleString()
  }
  return value.toString()
}

// 获取分组项的背景色
const getItemBgColor = (color: string) => {
  const colorMap: Record<string, string> = {
    blue: 'bg-blue-500',
    green: 'bg-green-500',
    purple: 'bg-purple-500',
    orange: 'bg-orange-500',
    red: 'bg-red-500',
    yellow: 'bg-yellow-500',
    teal: 'bg-teal-500',
    indigo: 'bg-indigo-500',
    pink: 'bg-pink-500',
    cyan: 'bg-cyan-500',
    lime: 'bg-lime-500',
    rose: 'bg-rose-500',
    emerald: 'bg-emerald-500'
  }
  return colorMap[color] || 'bg-gray-500'
}

// 获取分组项的文字色
const getItemTextColor = (color: string) => {
  const colorMap: Record<string, string> = {
    blue: 'text-blue-600',
    green: 'text-green-600',
    purple: 'text-purple-600',
    orange: 'text-orange-600',
    red: 'text-red-600',
    yellow: 'text-yellow-600',
    teal: 'text-teal-600',
    indigo: 'text-indigo-600',
    pink: 'text-pink-600',
    cyan: 'text-cyan-600',
    lime: 'text-lime-600',
    rose: 'text-rose-600',
    emerald: 'text-emerald-600'
  }
  return colorMap[color] || 'text-gray-600'
}

// 颜色类映射
const colorClasses = {
  blue: {
    border: 'border-l-blue-500',
    text: 'text-blue-600',
    background: 'bg-blue-500'
  },
  green: {
    border: 'border-l-green-500',
    text: 'text-green-600',
    background: 'bg-green-500'
  },
  purple: {
    border: 'border-l-purple-500',
    text: 'text-purple-600',
    background: 'bg-purple-500'
  },
  orange: {
    border: 'border-l-orange-500',
    text: 'text-orange-600',
    background: 'bg-orange-500'
  },
  red: {
    border: 'border-l-red-500',
    text: 'text-red-600',
    background: 'bg-red-500'
  },
  yellow: {
    border: 'border-l-yellow-500',
    text: 'text-yellow-600',
    background: 'bg-yellow-500'
  },
  teal: {
    border: 'border-l-teal-500',
    text: 'text-teal-600',
    background: 'bg-teal-500'
  },
  indigo: {
    border: 'border-l-indigo-500',
    text: 'text-indigo-600',
    background: 'bg-indigo-500'
  },
  pink: {
    border: 'border-l-pink-500',
    text: 'text-pink-600',
    background: 'bg-pink-500'
  },
  cyan: {
    border: 'border-l-cyan-500',
    text: 'text-cyan-600',
    background: 'bg-cyan-500'
  },
  lime: {
    border: 'border-l-lime-500',
    text: 'text-lime-600',
    background: 'bg-lime-500'
  },
  rose: {
    border: 'border-l-rose-500',
    text: 'text-rose-600',
    background: 'bg-rose-500'
  },
  emerald: {
    border: 'border-l-emerald-500',
    text: 'text-emerald-600',
    background: 'bg-emerald-500'
  }
}

const borderColorClass = computed(() => colorClasses[props.color].border)
const textColorClass = computed(() => colorClasses[props.color].text)
const backgroundColorClass = computed(() => colorClasses[props.color].background)
</script>

<style scoped>
.data-card {
  min-height: 100px;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}
</style>
