"""
七鱼API客户端服务

直接使用七鱼服务模块，避免过度封装
"""
import logging
from typing import List, Dict, Optional
from datetime import datetime, timedelta
from urllib.parse import urlparse, parse_qs
import re

from apps.common.qiyu_service import get_qiyu_service
from apps.cs_manage.models import CsEmotionAnalysis

logger = logging.getLogger(__name__)


class QiyuDataClient:
    """七鱼数据客户端 - 提供业务相关的便利方法"""

    def __init__(self):
        self.qiyu_service = get_qiyu_service()
    
    def _get_day_timestamps(self, target_date: datetime) -> tuple[int, int]:
        """
        获取指定日期的开始和结束时间戳（毫秒）

        确保时间范围是：00:00:00 到 23:59:59.999
        """
        # 确保target_date是当日00:00:00
        start_datetime = target_date.replace(hour=0, minute=0, second=0, microsecond=0)
        # 结束时间是当日23:59:59.999
        end_datetime = target_date.replace(hour=23, minute=59, second=59, microsecond=999000)

        start_time = int(start_datetime.timestamp() * 1000)
        end_time = int(end_datetime.timestamp() * 1000)

        logger.debug(f"[七鱼客户端] 时间范围: {start_datetime} 到 {end_datetime}")
        logger.debug(f"[七鱼客户端] 时间戳: {start_time} 到 {end_time}")

        return start_time, end_time
    
    def get_staff_groups(self, use_cache: bool = True) -> List[Dict]:
        """
        获取客服组列表

        Args:
            use_cache: 是否使用缓存

        Returns:
            客服组列表，包含id、name等字段
        """
        try:
            groups = self.qiyu_service.get_staff_group_list(staff=False, empty_group=False, use_cache=use_cache)

            if not groups:
                logger.warning("[七鱼客户端] 获取客服组列表为空")
                return []

            logger.info(f"[七鱼客户端] 获取客服组列表成功，共{len(groups)}个组")
            return groups

        except Exception as e:
            logger.error(f"[七鱼客户端] 获取客服组列表失败: {str(e)}")
            return []
    
    def get_session_history_by_date(self, target_date: datetime) -> List[Dict]:
        """
        获取指定日期的会话历史记录

        Args:
            target_date: 目标日期

        Returns:
            会话历史记录列表
        """
        try:
            start_time, end_time = self._get_day_timestamps(target_date)
            sessions = self.qiyu_service.get_session_history(start_time, end_time)

            if not sessions:
                logger.warning(f"[七鱼客户端] {target_date.date()} 会话历史记录为空")
                return []

            logger.info(f"[七鱼客户端] 获取 {target_date.date()} 会话历史记录成功，共{len(sessions)}条")
            return sessions

        except Exception as e:
            logger.error(f"[七鱼客户端] 获取 {target_date.date()} 会话历史记录失败: {str(e)}")
            return []
    
    def get_worksheet_list_by_date(self, target_date: datetime, group_ids: List[int] = None) -> List[Dict]:
        """
        获取指定日期的工单列表

        Args:
            target_date: 目标日期
            group_ids: 组别ID列表，为空则获取全部

        Returns:
            工单列表
        """
        try:
            start_time, end_time = self._get_day_timestamps(target_date)
            worksheets = self.qiyu_service.get_worksheet_list(start_time, end_time, group_select_list=group_ids or [])

            logger.info(f"[七鱼客户端] 获取 {target_date.date()} 工单列表成功，共{len(worksheets)}条")
            return worksheets

        except Exception as e:
            logger.error(f"[七鱼客户端] 获取 {target_date.date()} 工单列表失败: {str(e)}")
            return []
    
    def get_statistics_overview_by_date(self, target_date: datetime, group_id: int = 0) -> Optional[Dict]:
        """
        获取指定日期的统计概览

        Args:
            target_date: 目标日期
            group_id: 客服组ID，0表示全部

        Returns:
            统计概览数据
        """
        try:
            start_time, end_time = self._get_day_timestamps(target_date)

            # 根据group_id构建groupIds参数
            group_ids = ""
            if group_id != 0:
                group_ids = str(group_id)

            overview = self.qiyu_service.get_statistics_overview(start_time, end_time, group_ids)

            if not overview:
                group_display = f"组别{group_id}" if group_id != 0 else "全部"
                logger.warning(f"[七鱼客户端] {target_date.date()} {group_display} 统计概览为空")
                return None

            group_display = f"组别{group_id}" if group_id != 0 else "全部"
            logger.info(f"[七鱼客户端] 获取 {target_date.date()} {group_display} 统计概览成功")
            return overview

        except Exception as e:
            group_display = f"组别{group_id}" if group_id != 0 else "全部"
            logger.error(f"[七鱼客户端] 获取 {target_date.date()} {group_display} 统计概览失败: {str(e)}")
            return None
    
    def get_emotion_analysis_by_date(self, target_date: datetime) -> Optional[float]:
        """
        获取指定日期的情绪分析数据
        
        Args:
            target_date: 目标日期
            
        Returns:
            平均情绪分值
        """
        try:
            # 查询当日的情绪分析数据
            start_datetime = target_date.replace(hour=0, minute=0, second=0, microsecond=0)
            end_datetime = start_datetime + timedelta(days=1)
            
            emotion_data = CsEmotionAnalysis.objects.filter(
                create_datetime__gte=start_datetime,
                create_datetime__lt=end_datetime,
                status='completed'
            ).values_list('final_emotion_score', flat=True)
            
            if not emotion_data:
                logger.info(f"[七鱼客户端] {target_date.date()} 无情绪分析数据")
                return None
                
            # 计算平均值
            valid_scores = [score for score in emotion_data if score is not None]
            if not valid_scores:
                return None
                
            avg_score = sum(valid_scores) / len(valid_scores)
            logger.info(f"[七鱼客户端] 获取 {target_date.date()} 情绪分析成功，平均分: {avg_score:.2f}")
            return round(avg_score, 2)
            
        except Exception as e:
            logger.error(f"[七鱼客户端] 获取 {target_date.date()} 情绪分析失败: {str(e)}")
            return None

    @staticmethod
    def is_ai_transfer_session(session: Dict) -> bool:
        """
        判断是否为AI转人工会话（更精确的判断）

        这个方法专门用于判断AI转人工的场景，使用更严格的规则。

        Args:
            session: 会话数据

        Returns:
            是否为AI转人工会话
        """
        try:
            # 获取来源页面URL
            session_ext = session.get('sessionExt', {})
            from_page = session_ext.get('fromPage', '')

            if not from_page:
                return False

            # 规则1: 使用正则表达式检查是否存在 u=...#数字 的非标准模式
            # 这通常表示从AI会话转到人工
            if re.search(r'[?&]u=[^&#]*#\d+', from_page):
                return True

            # 规则2: 检查categoryDescription或route字段不等于"AI客服组"
            # 这表示从AI客服组转到其他客服组
            category_description = session.get('categoryDescription', '')
            route = session.get('route', '')
            
            # 如果categoryDescription或route包含"AI客服组"，则不是AI转人工
            if 'AI客服组' in category_description or 'AI客服组' in route:
                return False

            return False

        except Exception as e:
            logger.debug(f"[七鱼客户端] 判断AI转人工失败: {str(e)}")
            return False

    # 向后兼容的别名方法
    @staticmethod
    def is_ai_session(session: Dict) -> bool:
        """
        判断是否为AI相关会话

        Args:
            session: 会话数据

        Returns:
            是否为AI相关会话
        """
        try:
            # 获取来源页面URL
            session_ext = session.get('sessionExt', {})
            from_page = session_ext.get('fromPage', '')

            if not from_page:
                return False

            # 规则1: 检查 URL 路径中是否包含 '/contactCs/'
            # 这是AI转人工的特定路径标识
            if '/contactCs/' in from_page:
                return True

            # 规则2: 检查categoryDescription或route字段是否包含"AI客服组"
            category_description = session.get('categoryDescription', '')
            route = session.get('route', '')
            
            if 'AI客服组' in category_description or 'AI客服组' in route:
                return True

            # 如果以上所有规则都不满足，则返回False
            return False

        except Exception as e:
            logger.debug(f"[七鱼客户端] 判断AI相关会话失败: {str(e)}")
            return False
