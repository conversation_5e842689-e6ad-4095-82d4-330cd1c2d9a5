#!/usr/bin/env python
"""
数据迁移脚本：将用户的部门从ForeignKey迁移到ManyToManyField
"""
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings')
django.setup()

from dvadmin.system.models import Users, Dept


def migrate_user_dept_data():
    """迁移用户部门数据"""
    print("开始迁移用户部门数据...")
    
    # 获取所有用户
    users = Users.objects.all()
    migrated_count = 0
    
    for user in users:
        # 检查用户是否有dept_belong_id（这是原来的主部门ID）
        if hasattr(user, 'dept_belong_id') and user.dept_belong_id:
            try:
                # 查找对应的部门
                dept = Dept.objects.get(id=user.dept_belong_id)
                
                # 检查是否已经关联了这个部门
                if not user.dept.filter(id=dept.id).exists():
                    # 添加到多对多关系中
                    user.dept.add(dept)
                    migrated_count += 1
                    print(f"用户 {user.username} 已关联到部门 {dept.name}")
                else:
                    print(f"用户 {user.username} 已经关联到部门 {dept.name}，跳过")
                    
            except Dept.DoesNotExist:
                print(f"警告：用户 {user.username} 的部门ID {user.dept_belong_id} 不存在")
            except Exception as e:
                print(f"错误：迁移用户 {user.username} 时发生异常：{e}")
    
    print(f"迁移完成！共迁移了 {migrated_count} 个用户的部门关系")


def verify_migration():
    """验证迁移结果"""
    print("\n验证迁移结果...")
    
    users_with_dept = Users.objects.filter(dept__isnull=False).distinct()
    users_without_dept = Users.objects.filter(dept__isnull=True)
    
    print(f"有部门关联的用户数量：{users_with_dept.count()}")
    print(f"没有部门关联的用户数量：{users_without_dept.count()}")
    
    # 显示一些示例
    print("\n部门关联示例：")
    for user in users_with_dept[:5]:
        dept_names = [dept.name for dept in user.dept.all()]
        print(f"用户 {user.username}: {', '.join(dept_names)}")


if __name__ == "__main__":
    try:
        migrate_user_dept_data()
        verify_migration()
    except Exception as e:
        print(f"迁移过程中发生错误：{e}")
        sys.exit(1)
