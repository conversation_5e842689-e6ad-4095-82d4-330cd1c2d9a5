"""
超自然数据分析器
实现登录数据分析、异常时间点检测、IP地址解析等功能
"""
import base64
import json
import logging
import zlib
from datetime import datetime, time, timedelta
from typing import Dict, Any, List, Tuple

import pandas as pd
import numpy as np
from collections import Counter

from apps.refund.games.base.analyzer import BaseAnalyzer

# 用于JSON序列化numpy类型
class NumpyEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        return super(NumpyEncoder, self).default(obj)

logger = logging.getLogger(__name__)


class ChaoziranAnalyzer(BaseAnalyzer):
    """超自然数据分析器"""

    def __init__(self, uid: int, start_time: str, end_time: str, sql_data: Dict[str, Any]):
        # 按照基类的参数顺序调用父类构造函数
        # BaseAnalyzer.__init__(self, data, start_time, end_time, error_phrases, uid)
        super().__init__(
            data=sql_data,           # data 参数
            start_time=start_time,   # start_time 参数
            end_time=end_time,       # end_time 参数
            error_phrases=[],        # error_phrases 参数（超自然暂不使用）
            uid=uid                  # uid 参数
        )

        # 重新赋值以保持一致性
        self.sql_data = sql_data

        # 初始化分析结果存储
        self.user_info = {}
        self.login_analysis = {}
        self.payment_analysis = {}
        self.gift_analysis = {}
        self.abnormal_login_times = []
        self.ip_locations = {}

    def initialize_metrics(self):
        """初始化分析指标"""
        logger.info(f"超自然分析器初始化，用户ID: {self.uid}")

        # 初始化各项分析指标
        self.login_analysis = {
            'total_login_days': 0,
            'total_login_times': 0,
            'login_frequency': {},
            'login_time_distribution': {},
            'abnormal_login_times': [],
            'ip_locations': {},
            'device_info': {}
        }

        self.payment_analysis = {
            'total_amount': 0,
            'total_orders': 0,
            'payment_frequency': {},
            'product_analysis': {}
        }

        self.gift_analysis = {
            'total_gifts_sent': 0,
            'total_gifts_received': 0,
            'gift_types': {},
            'gift_frequency': {}
        }

    def _fetch_user_basic_info(self):
        """获取用户基础信息"""
        try:
            # 延迟导入避免循环依赖
            from apps.czr_gmt.utils.fetch_user_info import fetch_user_info
            from apps.czr_gmt.utils.requests_tools import czrGmt

            gmt = czrGmt()
            user_data = {"account": str(self.uid)}
            self.user_info = fetch_user_info(user_data, gmt)
            logger.info(f"获取用户基础信息成功，用户ID: {self.uid}")
        except Exception as e:
            logger.error(f"获取用户基础信息失败，用户ID: {self.uid}, 错误: {e}")
            self.user_info = {}

    def process_data(self):
        """处理原始数据"""
        logger.info(f"开始处理超自然数据，用户ID: {self.uid}")

        # 处理登录数据
        self._process_login_data()

        # 处理支付数据
        self._process_payment_data()

        # 处理赠礼数据
        self._process_gift_data()

        # 获取基础用户数据
        self._fetch_user_basic_info()

        # 生成图表分析数据（在所有数据处理完成后）
        login_data = self.sql_data.get('login_info', {}).get('data', [])
        sorted_login_data = sorted(login_data, key=lambda x: x.get('time', ''))
        self._generate_chart_analysis_data(sorted_login_data)

        logger.info(f"数据处理完成，用户ID: {self.uid}")

    def _process_login_data(self):
        """处理登录数据"""
        login_data = self.sql_data.get('login_info', {}).get('data', [])
        if not login_data:
            logger.warning(f"用户 {self.uid} 无登录数据")
            return

        # 按时间排序登录数据
        sorted_login_data = sorted(login_data, key=lambda x: x.get('time', ''))

        # 统计基础信息
        self.login_analysis['total_login_times'] = len(sorted_login_data)

        # 统计登录天数
        login_dates = set()
        login_hours = []
        ips = set()
        devices = []

        for login in sorted_login_data:
            # 提取日期
            login_time = login.get('time', '')
            if login_time:
                date_part = login_time.split(' ')[0]
                login_dates.add(date_part)

                # 提取小时用于时间分布分析
                try:
                    hour = int(login_time.split(' ')[1].split(':')[0])
                    login_hours.append(hour)
                except:
                    pass

            # 收集IP
            ip = login.get('client_ip', '')
            if ip:
                ips.add(ip)

            # 收集设备信息
            device = login.get('device', '')
            if device:
                devices.append(device)

        self.login_analysis['total_login_days'] = len(login_dates)

        # 分析登录时间分布
        hour_distribution = Counter(login_hours)
        self.login_analysis['login_time_distribution'] = dict(hour_distribution)

        # 分析设备信息
        device_distribution = Counter(devices)
        self.login_analysis['device_info'] = dict(device_distribution)

        # 检测异常登录时间
        self._detect_abnormal_login_times(sorted_login_data)

        # 解析IP地址
        self._resolve_ip_locations(list(ips))



    def _detect_abnormal_login_times(self, login_data: List[Dict]):
        """检测异常登录时间"""
        abnormal_logins = []

        for login in login_data:
            login_time_str = login.get('time', '')
            if not login_time_str:
                continue

            try:
                login_datetime = datetime.strptime(login_time_str, '%Y-%m-%d %H:%M:%S')

                # 检查是否为异常时间
                if self._is_abnormal_time(login_datetime):
                    abnormal_login = login.copy()
                    abnormal_login['abnormal_reason'] = self._get_abnormal_reason(login_datetime)
                    abnormal_logins.append(abnormal_login)

            except Exception as e:
                logger.warning(f"解析登录时间失败: {login_time_str}, 错误: {e}")

        self.login_analysis['abnormal_login_times'] = abnormal_logins
        logger.info(f"用户 {self.uid} 检测到 {len(abnormal_logins)} 次异常登录")

    def _generate_chart_analysis_data(self, login_data: List[Dict]):
        """生成图表分析数据"""
        if not login_data:
            self.login_analysis['chart_data'] = {}
            return

        # 1. 热力图数据 - 每周每小时的活跃度
        heatmap_data = self._generate_heatmap_data(login_data)

        # 2. 散点图数据 - 原始登录分布
        scatter_data = self._generate_scatter_data(login_data)

        # 3. 未成年人行为分析图表
        minor_behavior_data = self._generate_minor_behavior_analysis(login_data)

        self.login_analysis['chart_data'] = {
            'heatmap': heatmap_data,
            'scatter': scatter_data,
            'minor_behavior': minor_behavior_data
        }

        logger.info(f"用户 {self.uid} 图表分析数据生成完成")

    def _generate_heatmap_data(self, login_data: List[Dict]) -> List[List]:
        """生成热力图数据 - 每周每小时的活跃度"""
        # 初始化7天x24小时的矩阵
        heatmap_matrix = [[0 for _ in range(24)] for _ in range(7)]

        for login in login_data:
            login_time_str = login.get('time', '')
            if not login_time_str:
                continue

            try:
                login_datetime = datetime.strptime(login_time_str, '%Y-%m-%d %H:%M:%S')
                weekday = login_datetime.weekday()  # 0=周一, 6=周日
                hour = login_datetime.hour
                heatmap_matrix[weekday][hour] += 1
            except Exception as e:
                logger.warning(f"解析登录时间失败: {login_time_str}, 错误: {e}")
                continue

        # 转换为ECharts热力图格式 [hour, day, value] - 修正坐标轴
        heatmap_data = []
        for day in range(7):
            for hour in range(24):
                heatmap_data.append([hour, day, heatmap_matrix[day][hour]])

        return heatmap_data

    def _generate_scatter_data(self, login_data: List[Dict]) -> List[Dict]:
        """生成散点图数据 - 原始登录分布"""
        scatter_data = []

        for login in login_data:
            login_time_str = login.get('time', '')
            if not login_time_str:
                continue

            try:
                login_datetime = datetime.strptime(login_time_str, '%Y-%m-%d %H:%M:%S')

                # 计算从开始日期的天数偏移
                start_date = datetime.strptime(self.start_time, '%Y-%m-%d')
                days_offset = (login_datetime.date() - start_date.date()).days

                # 时间转换为小时的小数形式
                time_decimal = login_datetime.hour + login_datetime.minute / 60.0

                scatter_data.append({
                    'date': days_offset,
                    'time': time_decimal,
                    'datetime': login_time_str,
                    'is_abnormal': self._is_abnormal_time(login_datetime)
                })
            except Exception as e:
                logger.warning(f"解析登录时间失败: {login_time_str}, 错误: {e}")
                continue

        return scatter_data



    def _generate_minor_behavior_analysis(self, login_data: List[Dict]) -> Dict[str, Any]:
        """生成未成年人行为分析数据"""
        analysis_data = {
            'time_pattern': {},  # 时间模式分析
            'risk_assessment': {},  # 风险评估
            'behavior_summary': {}  # 行为总结
        }

        if not login_data:
            return analysis_data

        # 按时间段统计登录次数
        time_segments = {
            'early_morning': 0,  # 6:00-8:00
            'morning': 0,        # 8:00-12:00
            'afternoon': 0,      # 12:00-18:00
            'evening': 0,        # 18:00-22:00
            'night': 0,          # 22:00-23:00
            'late_night': 0      # 23:00-6:00
        }

        workday_abnormal = 0
        weekend_abnormal = 0
        total_logins = len(login_data)

        for login in login_data:
            login_time_str = login.get('time', '')
            if not login_time_str:
                continue

            try:
                login_datetime = datetime.strptime(login_time_str, '%Y-%m-%d %H:%M:%S')
                hour = login_datetime.hour
                weekday = login_datetime.weekday()

                # 检查是否为节假日
                try:
                    from apps.refund.utils.calender import calender
                    # calender函数需要两个参数：开始时间和结束时间
                    # 这里我们只检查单个日期，所以开始和结束时间都是同一天
                    date_str = login_datetime.date().strftime('%Y-%m-%d')
                    calendar_result = calender(date_str, date_str)
                    # calender返回的是一个列表，包含日期信息，我们取第一个元素的is_work字段
                    is_holiday = not calendar_result[0]['is_work'] if calendar_result else False
                except Exception:
                    is_holiday = False

                # 统计时间段
                if 6 <= hour < 8:
                    time_segments['early_morning'] += 1
                elif 8 <= hour < 12:
                    time_segments['morning'] += 1
                elif 12 <= hour < 18:
                    time_segments['afternoon'] += 1
                elif 18 <= hour < 22:
                    time_segments['evening'] += 1
                elif 22 <= hour < 23:
                    time_segments['night'] += 1
                else:
                    time_segments['late_night'] += 1

                # 统计异常登录
                if self._is_abnormal_time(login_datetime):
                    if not is_holiday and weekday < 5:
                        workday_abnormal += 1
                    else:
                        weekend_abnormal += 1

            except Exception as e:
                logger.warning(f"解析登录时间失败: {login_time_str}, 错误: {e}")
                continue

        # 计算风险指标
        abnormal_rate = (workday_abnormal + weekend_abnormal) / max(total_logins, 1)
        school_time_rate = time_segments['morning'] / max(total_logins, 1)
        late_night_rate = time_segments['late_night'] / max(total_logins, 1)

        analysis_data['time_pattern'] = time_segments
        analysis_data['risk_assessment'] = {
            'abnormal_rate': round(abnormal_rate * 100, 2),
            'workday_abnormal': workday_abnormal,
            'weekend_abnormal': weekend_abnormal,
            'school_time_rate': round(school_time_rate * 100, 2),
            'late_night_rate': round(late_night_rate * 100, 2)
        }

        # 行为总结
        risk_level = "低风险"
        if abnormal_rate > 0.3:
            risk_level = "高风险"
        elif abnormal_rate > 0.15:
            risk_level = "中风险"

        analysis_data['behavior_summary'] = {
            'risk_level': risk_level,
            'total_logins': total_logins,
            'abnormal_logins': workday_abnormal + weekend_abnormal,
            'main_active_time': max(time_segments, key=time_segments.get)
        }

        logger.info(f"用户 {self.uid} 未成年人行为分析完成，风险等级: {risk_level}")
        return analysis_data

    def _is_abnormal_time(self, login_datetime: datetime) -> bool:
        """判断是否为异常登录时间 - 针对未成年人的严格判定"""
        # 获取登录的日期和时间
        login_date = login_datetime.date()
        login_hour = login_datetime.hour
        login_minute = login_datetime.minute
        weekday = login_datetime.weekday()  # 0=周一, 6=周日

        # 检查是否为节假日
        try:
            from apps.refund.utils.calender import calender
            # calender函数需要两个参数：开始时间和结束时间
            # 这里我们只检查单个日期，所以开始和结束时间都是同一天
            date_str = login_date.strftime('%Y-%m-%d')
            calendar_result = calender(date_str, date_str)
            # calender返回的是一个列表，包含日期信息，我们取第一个元素的is_work字段
            is_holiday = not calendar_result[0]['is_work'] if calendar_result else False
        except Exception:
            # 如果日历功能不可用，默认不是节假日
            is_holiday = False

        # 工作日判定
        if not is_holiday and weekday < 5:  # 工作日
            # 上学时间 (8:00-17:00) 算异常
            if 8 <= login_hour <= 17:
                return True
            # 晚上10点以后登录异常 (家长管控时间)
            if login_hour >= 22:
                return True
            # 早上6点之前登录异常
            if login_hour <= 6:
                return True

        # 节假日和周末判定
        else:  # 节假日或周末
            # 深夜凌晨时间 (23:00-7:00) 算异常
            if login_hour >= 23 or login_hour <= 7:
                return True

        return False

    def _get_abnormal_reason(self, login_datetime: datetime) -> str:
        """获取异常登录的原因"""
        login_hour = login_datetime.hour
        weekday = login_datetime.weekday()

        # 检查是否为节假日
        try:
            from apps.refund.utils.calender import calender
            # calender函数需要两个参数：开始时间和结束时间
            # 这里我们只检查单个日期，所以开始和结束时间都是同一天
            date_str = login_datetime.date().strftime('%Y-%m-%d')
            calendar_result = calender(date_str, date_str)
            # calender返回的是一个列表，包含日期信息，我们取第一个元素的is_work字段
            is_holiday = not calendar_result[0]['is_work'] if calendar_result else False
        except Exception:
            is_holiday = False

        # 工作日判定
        if not is_holiday and weekday < 5:
            if 8 <= login_hour <= 17:
                return "工作日上学时间登录"
            elif login_hour >= 22:
                return "工作日深夜登录(家长管控时间)"
            elif login_hour <= 6:
                return "工作日早晨登录"
        # 节假日和周末判定
        else:
            if login_hour >= 23 or login_hour <= 7:
                return "节假日/周末深夜登录"

        return "其他异常时间"

    def _resolve_ip_locations(self, ips: List[str]):
        """解析IP地址位置"""
        if not ips:
            return

        # 确保ip_locations键存在
        if 'ip_locations' not in self.login_analysis:
            self.login_analysis['ip_locations'] = {}

        try:
            from apps.refund.utils.ip_search.main import searcher
            ip_results = searcher(ips)
            for result in ip_results:
                ip = result.get("ip", "")
                if ip:
                    self.login_analysis['ip_locations'][ip] = {
                        'country': result.get('country', ''),
                        'province': result.get('province', ''),
                        'city': result.get('city', ''),
                        'isp': result.get('isp', ''),
                        'location': f"{result.get('province', '')}{result.get('city', '')}"
                    }
            logger.info(f"用户 {self.uid} IP地址解析完成，共 {len(ip_results)} 个IP")
        except Exception as e:
            logger.error(f"IP地址解析失败，用户ID: {self.uid}, 错误: {e}")
            # IP解析失败时，至少记录IP地址
            for ip in ips:
                self.login_analysis['ip_locations'][ip] = {
                    'country': '未知',
                    'province': '未知',
                    'city': '未知',
                    'isp': '未知',
                    'location': '未知'
                }

    def _process_payment_data(self):
        """处理支付数据"""
        payment_data = self.sql_data.get('pay_info', {}).get('data', [])
        if not payment_data:
            logger.warning(f"用户 {self.uid} 无支付数据")
            return

        # 统计基础信息
        self.payment_analysis['total_orders'] = len(payment_data)

        total_amount = 0
        product_counter = Counter()
        payment_dates = []

        for payment in payment_data:
            # 累计金额
            amount = payment.get('amount', 0)
            if amount:
                try:
                    total_amount += float(amount)
                except:
                    pass

            # 统计产品
            product_id = payment.get('product_id', '')
            if product_id:
                product_counter[product_id] += 1

            # 收集支付日期
            payment_time = payment.get('time', '')
            if payment_time:
                date_part = payment_time.split(' ')[0]
                payment_dates.append(date_part)

        self.payment_analysis['total_amount'] = total_amount
        self.payment_analysis['product_analysis'] = dict(product_counter)

        # 分析支付频率
        date_counter = Counter(payment_dates)
        self.payment_analysis['payment_frequency'] = dict(date_counter)

        # 按日期统计支付金额（用于图表分析）
        daily_payment_amount = {}
        for payment in payment_data:
            payment_time = payment.get('time', '')
            amount = payment.get('amount', 0)
            if payment_time and amount:
                try:
                    date_part = payment_time.split(' ')[0]
                    amount_float = float(amount)
                    daily_payment_amount[date_part] = daily_payment_amount.get(date_part, 0) + amount_float
                except Exception as e:
                    logger.warning(f"处理支付金额失败: {payment_time}, {amount}, 错误: {e}")
                    continue

        self.payment_analysis['daily_payment_amount'] = daily_payment_amount

        logger.info(f"用户 {self.uid} 支付数据处理完成，总金额: {total_amount}, 订单数: {len(payment_data)}")

    def _process_gift_data(self):
        """处理赠礼数据"""
        gift_data = self.sql_data.get('gift_send', {}).get('data', [])
        if not gift_data:
            logger.warning(f"用户 {self.uid} 无赠礼数据")
            return

        # 统计基础信息
        self.gift_analysis['total_gifts_sent'] = len(gift_data)

        gift_types = Counter()
        gift_dates = []
        total_numbers = 0

        for gift in gift_data:
            # 统计礼物类型
            gift_info = gift.get('gift_info', {})
            gift_name = gift_info.get('gift_name', '')
            if gift_name:
                gift_types[gift_name] += 1

            # 统计数量
            numbers = gift_info.get('numbers', 0)
            if numbers:
                try:
                    total_numbers += int(numbers)
                except:
                    pass

            # 收集赠礼日期
            gift_time = gift.get('time', '')
            if gift_time:
                date_part = gift_time.split(' ')[0]
                gift_dates.append(date_part)

        self.gift_analysis['gift_types'] = dict(gift_types)
        self.gift_analysis['total_gift_numbers'] = total_numbers

        # 分析赠礼频率
        date_counter = Counter(gift_dates)
        self.gift_analysis['gift_frequency'] = dict(date_counter)

        logger.info(f"用户 {self.uid} 赠礼数据处理完成，总礼物数: {total_numbers}, 赠送次数: {len(gift_data)}")

    def generate_score_report(self) -> Dict[str, Any]:
        """生成评分报告"""
        logger.info(f"开始生成超自然评分报告，用户ID: {self.uid}")

        # 基础风险评分
        risk_score = 0
        risk_factors = []

        # 异常登录风险评分
        abnormal_logins = self.login_analysis.get('abnormal_login_times', [])
        if abnormal_logins:
            abnormal_count = len(abnormal_logins)
            if abnormal_count > 10:
                risk_score += 30
                risk_factors.append(f"频繁异常时间登录({abnormal_count}次)")
            elif abnormal_count > 5:
                risk_score += 20
                risk_factors.append(f"多次异常时间登录({abnormal_count}次)")
            elif abnormal_count > 0:
                risk_score += 10
                risk_factors.append(f"存在异常时间登录({abnormal_count}次)")

        # 支付行为风险评分
        total_amount = self.payment_analysis.get('total_amount', 0)
        if total_amount > 1000:
            risk_score += 20
            risk_factors.append(f"高额消费({total_amount}元)")
        elif total_amount > 500:
            risk_score += 10
            risk_factors.append(f"中等消费({total_amount}元)")

        # 登录频率风险评分
        total_login_times = self.login_analysis.get('total_login_times', 0)
        total_login_days = self.login_analysis.get('total_login_days', 1)
        avg_login_per_day = total_login_times / max(total_login_days, 1)

        if avg_login_per_day > 10:
            risk_score += 15
            risk_factors.append(f"高频登录(日均{avg_login_per_day:.1f}次)")
        elif avg_login_per_day > 5:
            risk_score += 10
            risk_factors.append(f"频繁登录(日均{avg_login_per_day:.1f}次)")

        print(self.user_info)
        # 生成综合报告
        report = {
            'user_info': self.user_info,
            'analysis_summary': {
                'risk_score': min(risk_score, 100),  # 最高100分
                'risk_level': self._get_risk_level(risk_score),
                'risk_factors': risk_factors
            },
            'login_analysis': self.login_analysis,
            'payment_analysis': self.payment_analysis,
            'gift_analysis': self.gift_analysis,
            'raw_data': self.sql_data,
            'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'uid': self.uid,
            'time_range': f"{self.start_time} 至 {self.end_time}"
        }

        return report

    def _get_risk_level(self, score: int) -> str:
        """根据分数获取风险等级"""
        if score >= 70:
            return "高风险"
        elif score >= 40:
            return "中风险"
        elif score >= 20:
            return "低风险"
        else:
            return "正常"

    def calculate_game_time(self):
        """计算游戏时长相关指标 - 超自然暂无此数据"""
        logger.debug(f"超自然暂无游戏时长数据，用户ID: {self.uid}")
        pass

    def check_login_address(self):
        """检查登录地址异常 - 已在登录数据分析中实现"""
        logger.debug(f"登录地址检查已在 _resolve_ip_locations 中实现，用户ID: {self.uid}")
        pass

    def detect_error_phrases(self):
        """检测错误聊天内容 - 超自然暂无聊天数据"""
        logger.debug(f"超自然暂无聊天数据，用户ID: {self.uid}")
        pass

    def analyze_device_usage(self):
        """分析设备使用情况 - 已在登录数据分析中实现"""
        logger.debug(f"设备使用分析已在 _process_login_data 中实现，用户ID: {self.uid}")
        pass

    def check_recharge(self):
        """检查充值行为 - 已在支付数据分析中实现"""
        logger.debug(f"充值行为检查已在 _process_payment_data 中实现，用户ID: {self.uid}")
        pass

    def check_gift_send(self):
        """检查送礼行为 - 已在赠礼数据分析中实现"""
        logger.debug(f"送礼行为检查已在 _process_gift_data 中实现，用户ID: {self.uid}")
        pass

    def get_user_base_info(self) -> Dict[str, Any]:
        """获取用户基础信息"""
        return self.user_info

    def run_analysis(self) -> str:
        """运行完整的数据分析流程 - 重写基类方法"""
        try:
            logger.info(f"开始处理超自然用户 {self.uid} 的数据")

            self.process_data()

            # 生成报告
            report = self.generate_score_report()

            # 压缩并编码报告
            _data = json.dumps(report, ensure_ascii=False, cls=NumpyEncoder).encode('utf-8')
            compressed_data = zlib.compress(_data)
            b64_data = base64.b64encode(compressed_data)

            logger.info(f"超自然用户 {self.uid} 数据处理完成")
            return b64_data.decode('utf-8')

        except Exception as e:
            logger.error(f"超自然分析出错，用户ID: {self.uid}, 错误: {e}, 行号: {e.__traceback__.tb_lineno}")
            raise
