<template>
  <el-dialog
    v-model="dialogVisible"
    title="质检记录详情"
    width="80%"
    :before-close="handleClose"
    destroy-on-close
  >
    <div v-if="record" class="record-detail">
      <!-- 基本信息 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>会话ID：</label>
              <span>{{ record.session_id }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>客服姓名：</label>
              <span>{{ record.service_name }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>客服ID：</label>
              <span>{{ record.service_id }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>游戏：</label>
              <span>{{ record.game_name || record.game }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>关联用户：</label>
              <span>{{ record.user_info?.name || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>会话时长：</label>
              <span>{{ formatDuration(record.session_duration) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>会话开始：</label>
              <span>{{ formatDateTime(record.session_start_time) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>会话结束：</label>
              <span>{{ formatDateTime(record.session_end_time) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>质检时间：</label>
              <span>{{ formatDateTime(record.created_datetime) }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 质检结果 -->
      <el-card class="result-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>质检结果</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="score-item">
              <div class="score-value" :class="getScoreClass(record.overall_score)">
                {{ record.overall_score || 0 }}
              </div>
              <div class="score-label">总分</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="score-item">
              <div class="score-value deduction">
                -{{ record.total_deductions || 0 }}
              </div>
              <div class="score-label">总扣分</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="score-item">
              <div class="score-value">
                {{ record.deduction_count || 0 }}
              </div>
              <div class="score-label">扣分项数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="score-item">
              <div class="score-value">
                {{ record.deduction_items?.length || 0 }}
              </div>
              <div class="score-label">详细扣分项</div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 扣分项详情 -->
      <el-card v-if="record.deduction_items && record.deduction_items.length > 0" class="deduction-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>扣分项详情</span>
            <el-tag type="warning" size="small">共 {{ record.deduction_items.length }} 项</el-tag>
          </div>
        </template>
        <div class="deduction-list">
          <div 
            v-for="(item, index) in record.deduction_items" 
            :key="index" 
            class="deduction-item"
          >
            <div class="deduction-header">
              <div class="deduction-title">
                <el-tag type="danger" size="small">{{ item.deduction_item }}</el-tag>
                <span class="deduction-score">-{{ item.deduction_score }}分</span>
                <el-button 
                  type="text" 
                  size="small" 
                  @click="filterByDeduction(item.deduction_item)"
                >
                  筛选此项
                </el-button>
              </div>
            </div>
            <div v-if="item.deduction_reason" class="deduction-reason">
              <strong>扣分原因：</strong>
              <p>{{ item.deduction_reason }}</p>
            </div>
            <div v-if="item.related_content" class="deduction-content">
              <strong>相关内容：</strong>
              <p>{{ item.related_content }}</p>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 会话总结 -->
      <el-card v-if="record.session_summary" class="summary-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>会话总结</span>
          </div>
        </template>
        <div class="summary-content">
          <p>{{ record.session_summary }}</p>
        </div>
      </el-card>

      <!-- 分析摘要 -->
      <el-card v-if="record.analysis_summary" class="analysis-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>分析摘要</span>
          </div>
        </template>
        <div class="analysis-content">
          <p>{{ record.analysis_summary }}</p>
        </div>
      </el-card>

      <!-- 客服表现数据 -->
      <el-card v-if="record.service_performance" class="performance-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>客服表现数据</span>
          </div>
        </template>
        <div class="performance-content">
          <pre>{{ formatPerformanceData(record.service_performance) }}</pre>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="exportRecord">导出此记录</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import * as api from '../api';

interface Props {
  modelValue: boolean;
  record: any;
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'deduction-filter', deductionItem: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value)
});

// 格式化时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleString('zh-CN');
};

// 格式化时长
const formatDuration = (seconds: number) => {
  if (!seconds) return '-';
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}分${remainingSeconds}秒`;
};

// 获取分数样式类
const getScoreClass = (score: number) => {
  if (score >= 90) return 'excellent';
  if (score >= 80) return 'good';
  if (score >= 70) return 'average';
  return 'poor';
};

// 格式化表现数据
const formatPerformanceData = (data: any) => {
  if (typeof data === 'string') {
    try {
      return JSON.stringify(JSON.parse(data), null, 2);
    } catch {
      return data;
    }
  }
  return JSON.stringify(data, null, 2);
};

// 按扣分项筛选
const filterByDeduction = (deductionItem: string) => {
  emit('deduction-filter', deductionItem);
  handleClose();
};

// 导出记录
const exportRecord = async () => {
  try {
    const query = {
      session_id: props.record.session_id
    };
    await api.exportData(query);
    ElMessage.success('导出成功');
  } catch (error) {
    ElMessage.error('导出失败');
  }
};

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false;
};
</script>

<style scoped>
.record-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.info-card,
.result-card,
.deduction-card,
.summary-card,
.analysis-card,
.performance-card {
  margin-bottom: 20px;
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.info-item {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.info-item label {
  font-weight: 500;
  color: #606266;
  margin-right: 8px;
  min-width: 80px;
}

.score-item {
  text-align: center;
  padding: 20px;
  border: 1px solid #EBEEF5;
  border-radius: 8px;
  background-color: #FAFAFA;
}

.score-value {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
}

.score-value.excellent {
  color: #67C23A;
}

.score-value.good {
  color: #409EFF;
}

.score-value.average {
  color: #E6A23C;
}

.score-value.poor {
  color: #F56C6C;
}

.score-value.deduction {
  color: #F56C6C;
}

.score-label {
  font-size: 14px;
  color: #909399;
}

.deduction-list {
  max-height: 400px;
  overflow-y: auto;
}

.deduction-item {
  padding: 15px;
  border: 1px solid #EBEEF5;
  border-radius: 8px;
  margin-bottom: 15px;
  background-color: #FEF0F0;
}

.deduction-header {
  margin-bottom: 10px;
}

.deduction-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.deduction-score {
  font-weight: bold;
  color: #F56C6C;
}

.deduction-reason,
.deduction-content {
  margin-top: 10px;
}

.deduction-reason strong,
.deduction-content strong {
  color: #606266;
  display: block;
  margin-bottom: 5px;
}

.deduction-reason p,
.deduction-content p {
  margin: 0;
  padding: 8px;
  background-color: #FFFFFF;
  border-radius: 4px;
  border-left: 3px solid #F56C6C;
}

.summary-content,
.analysis-content {
  padding: 15px;
  background-color: #F5F7FA;
  border-radius: 8px;
  line-height: 1.6;
}

.performance-content {
  padding: 15px;
  background-color: #F5F7FA;
  border-radius: 8px;
}

.performance-content pre {
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-card__header) {
  padding: 15px 20px;
  background-color: #FAFAFA;
  border-bottom: 1px solid #EBEEF5;
}
</style>