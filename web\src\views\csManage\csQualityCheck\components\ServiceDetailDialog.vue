<template>
  <el-dialog
    v-model="visible"
    :title="`客服质检详情 - ${serviceInfo.service_name || '未知客服'}`"
    width="80%"
    top="5vh"
    @close="handleClose"
  >
    <!-- 客服基本信息 -->
    <el-card class="service-info-card" v-if="serviceInfo">
      <template #header>
        <span>客服基本信息</span>
      </template>
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="info-item">
            <div class="info-label">客服姓名</div>
            <div class="info-value">{{ serviceInfo.service_name || '-' }}</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="info-item">
            <div class="info-label">客服ID</div>
            <div class="info-value">{{ serviceInfo.service_id || '-' }}</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="info-item">
            <div class="info-label">客服账号</div>
            <div class="info-value">{{ serviceInfo.service_account || '-' }}</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="info-item">
            <div class="info-label">质检等级</div>
            <div class="info-value">
              <el-tag :type="getGradeTagType(serviceInfo.grade)">{{ serviceInfo.grade_text || '-' }}</el-tag>
            </div>
          </div>
        </el-col>
      </el-row>
      
      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="6">
          <div class="info-item">
            <div class="info-label">总会话数</div>
            <div class="info-value">{{ serviceInfo.session_count || 0 }}</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="info-item">
            <div class="info-label">平均分数</div>
            <div class="info-value">
              <el-tag :type="getScoreTagType(serviceInfo.avg_score)">
                {{ (serviceInfo.avg_score || 0).toFixed(1) }}
              </el-tag>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="info-item">
            <div class="info-label">总扣分</div>
            <div class="info-value">{{ serviceInfo.total_deductions || 0 }}</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="info-item">
            <div class="info-label">有扣分会话</div>
            <div class="info-value">{{ summary.deduction_sessions || 0 }}</div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <el-form :model="filterForm" inline>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="loadServiceDetail"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadServiceDetail">刷新</el-button>
          <el-button @click="exportRecords">导出记录</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 质检记录列表 -->
    <el-card title="质检记录">
      <el-table 
        :data="qualityRecords" 
        stripe 
        v-loading="loading"
        @row-click="viewRecordDetail"
        style="cursor: pointer;"
      >
        <el-table-column prop="session_id" label="会话ID" width="150" show-overflow-tooltip />
        <el-table-column prop="game_name" label="游戏" width="120" />
        <el-table-column prop="user_info" label="关联用户" width="120">
          <template #default="{ row }">
            <span v-if="row.user_info">{{ row.user_info.name }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="overall_score" label="总分" width="80">
          <template #default="{ row }">
            <el-tag :type="getScoreTagType(row.overall_score)">
              {{ row.overall_score }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="total_deductions" label="总扣分" width="80" />
        <el-table-column prop="session_duration_str" label="会话时长" width="100" />
        <el-table-column prop="deduction_count" label="扣分项" width="80" />
        <el-table-column prop="analysis_summary" label="分析摘要" min-width="200" show-overflow-tooltip />
        <el-table-column prop="session_start_time_str" label="会话开始时间" width="160" />
        <el-table-column prop="create_datetime_str" label="质检时间" width="160" />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click.stop="viewRecordDetail(row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-if="pagination.total_count > 0"
        v-model:current-page="pagination.current_page"
        v-model:page-size="pagination.page_size"
        :total="pagination.total_count"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="loadServiceDetail"
        @current-change="loadServiceDetail"
        style="margin-top: 20px; text-align: right;"
      />
    </el-card>

    <!-- 质检记录详情弹窗 -->
    <RecordDetailDialog
      v-model="recordDetailVisible"
      :record="selectedRecord"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import RecordDetailDialog from './RecordDetailDialog.vue'
// import { getServiceDetail } from '../api' // 已废弃，数据统一从dashboard接口获取

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  serviceId: {
    type: String,
    default: ''
  },
  serviceInfo: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const loading = ref(false)
const qualityRecords = ref([])
const summary = ref({})
const dateRange = ref([])
const filterForm = reactive({
  start_date: '',
  end_date: ''
})
const pagination = reactive({
  current_page: 1,
  page_size: 20,
  total_count: 0
})

// 弹窗控制
const recordDetailVisible = ref(false)
const selectedRecord = ref(null)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 获取分数标签类型
const getScoreTagType = (score: number) => {
  if (score >= 90) return 'success'
  if (score >= 80) return 'primary'
  if (score >= 70) return 'warning'
  return 'danger'
}

// 获取等级标签类型
const getGradeTagType = (grade: string) => {
  switch (grade) {
    case 'A': return 'success'
    case 'B': return 'primary'
    case 'C': return 'warning'
    case 'D': return 'danger'
    default: return 'info'
  }
}

// 加载客服详情
const loadServiceDetail = async () => {
  if (!props.serviceId) return
  
  loading.value = true
  try {
    // TODO: 使用新的API接口获取客服详情数据
    // 暂时使用空数据，避免编译错误
    qualityRecords.value = []
    summary.value = {}
    pagination.total_count = 0
    pagination.current_page = 1
    
    ElMessage.info('客服详情功能暂时不可用，请使用质检记录列表查看数据')
    
  } catch (error) {
    ElMessage.error('加载客服详情失败')
  } finally {
    loading.value = false
  }
}

// 查看记录详情
const viewRecordDetail = (record: any) => {
  selectedRecord.value = record
  recordDetailVisible.value = true
}

// 导出记录
const exportRecords = () => {
  ElMessage.info('导出功能开发中...')
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  qualityRecords.value = []
  summary.value = {}
  pagination.current_page = 1
  dateRange.value = []
}

// 监听serviceId变化
watch(() => props.serviceId, (newId) => {
  if (newId && props.modelValue) {
    loadServiceDetail()
  }
})

// 监听弹窗显示状态
watch(() => props.modelValue, (show) => {
  if (show && props.serviceId) {
    loadServiceDetail()
  }
})
</script>

<style scoped>
.service-info-card {
  margin-bottom: 20px;
}

.info-item {
  text-align: center;
  padding: 10px;
}

.info-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.info-value {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-card :deep(.el-card__body) {
  padding: 16px 20px;
}

:deep(.el-table__row) {
  cursor: pointer;
}

:deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}
</style>